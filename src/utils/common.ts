const UNAUTHORIZED_STATUS = [401];

export const toBinary = (deviceId: string): string => {
  const numericDeviceId = parseInt(deviceId.replace(/\D/g, ""), 10);
  const binaryDeviceId = numericDeviceId.toString(2).padStart(8, "0");
  return binaryDeviceId;
};

export const logoutUser = (error: any, navigate: (path: string) => void) => {
  if (UNAUTHORIZED_STATUS.includes(error.status)) {
    localStorage.clear();
    navigate("/login");
  }
};
 