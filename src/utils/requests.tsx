// import axios from "axios";
import axios from "axios";
import { logoutUser } from "./common";

const REACT_APP_API_BASE_URL = "http://localhost:5000/";

export const postRequest = async (route: any, data: any) => {
  let url = REACT_APP_API_BASE_URL + route;
  //   let url = process.env.REACT_APP_API_BASE_URL + route;
  const token = localStorage.getItem("token");
  try {
    return await axios.post(url, data, {
      headers: { Authorization: `Bearer ${token}` },
    });
  } catch (error: any) {
    if (error.response && error.response.data.message === "Invalid token") {
      console.error(`🚨 [INVALID TOKEN] Route: ${route}, Token: ${token}`);
    }
    throw error;
  }
};

export const putRequest = async (route: any, data: any) => {
  let url = REACT_APP_API_BASE_URL + route;
  const token = localStorage.getItem("token");
  return await axios.put(url, data, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
};

export const getRequest = async (
  route: string,
  params: any,
  navigate?: (path: string) => void
) => {
  let url = REACT_APP_API_BASE_URL + route;
  // let url = process.env.REACT_APP_API_BASE_URL + route;
  const token = localStorage.getItem("token");

  try {
    return await axios.get(url, {
      params,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
  } catch (error: any) {
    if (navigate) {
      logoutUser(error, navigate);
    }
    if (error.response && error.response.data.message === "Invalid token") {
      console.error(`🚨 [INVALID TOKEN] Route: ${route}, Token: ${token}`);
    }
    throw error;
  }
};

export const patchRequest = async (route: string, data: any) => {
  let url = process.env.REACT_APP_API_BASE_URL + route;
  const token = localStorage.getItem("token");
  try {
    return await axios.patch(url, data, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
  } catch (error) {
    throw error;
  }
};

export const deleteRequest = async (route: string) => {
  let url = REACT_APP_API_BASE_URL + route;
  const token = localStorage.getItem("token");

  return await axios.delete(url, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
};
 