.header-card {
    display: flex;
    background: white;
    justify-content: space-between;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.header-card h1 {
  color: #4a5d3a;
  margin-bottom: 5px;
  font-size: 30px;
}

.search-rack {
  display: flex;
  align-items: center;
}

.search-rack input {
  padding: 8px 15px;
  width: 300px;
  border: 1px solid #4a5d3a;
  border-radius: 4px;
  font-size: 14px;
}

.deploy-rack-card {
  padding: 20px;
  border-radius: 4px;
  background-color: #f7f7f7;
  margin-bottom: 20px;
}

.deploy-rack-card .category-dropdown-item {
  padding: 8px 5px;
}

.deploy-rack-form {
  display: flex;
  align-items: flex-end;
  flex-wrap: wrap;
}

.deploy-rack-form label {
  display: block;
  margin-bottom: 6px;
  font-weight: bold;
}

.deploy-rack-form input {
  padding: 10px;
  width: 200px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.deploy-btn {
  padding: 10px 16px;
  background-color: #4a5d3a;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.active-racks-card {
  background-color: white;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #ccc;
}

.rack-buzzer-active {
  background-color: #d1e7dd;
  border-radius: 10px 10px 0px 0px !important;
  border-bottom: 1px solid #ccc;
  animation: blink-bg 1s infinite;
}

.buzzing-active {
  padding: 10px;
  margin-top: 10px;
  border-radius: 10px;
  border: 1px solid green;
}

@keyframes blink-bg {
  0% {
    background-color: #a1e3c440;
  }
  50% {
    background-color: #badccc40; /* slightly different color for blink */
  }
  100% {
    background-color: #ffffff;
  }
}
.buzzing-btn{
  background: #4a5d3a;
  color: white;
}
.buzzing-btn:hover {
  background: #4a5d3a;
  color: white;
}
.verify-modal-body{
  padding: 0px 10px;
}

.card-header-inventory {
  padding: 2px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #4a5d3a;
  color: white;
  padding: 4px 16px;
}

.rack-table {
  width: 100%;
  border-collapse: collapse;
}

.rack-table th,
.rack-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  text-align: left;
}

.status-active {
  background-color: #28a745;
  color: white;
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 12px;
}
.verify-btn:hover {
  color: white;
}
.verify-btn {
  min-width: 130px;
  padding: 10px 16px;
  background-color: #24a0ed;
  text-decoration: none;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.verify-icon {
  position: absolute;
  right: 10px;
  top: 8px;
}
.delete-btn {
  background-color: #dc3545;
  color: white;
  padding: 5px;
  margin-right: 6px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  height: 38px;
  min-width: 90px;
}

.edit-btn:hover{
  background-color: #282828;
  border-color: #282828;
}

.edit-btn {
  background-color: #333;
  padding: 5px;
  margin-right: 6px;
  border: 1px solid #333;
  border-radius: 4px;
  cursor: pointer;
  height: 38px;
  min-width: 90px;
  color: #fff;
}

.edit-btn:hover{
  border-color: #333;
}

.delete-btn {
  background-color: #dc3545;
  padding: 5px;
  margin-left: 6px;
  border: 1px solid #dc3545;
  border-radius: 4px;
  cursor: pointer;
  height: 38px;
  min-width: 90px;
  color: #fff;
}

.delete-btn:hover{
  background-color: #c82333;
  border-color: #c82333;
}

.locate-btn {
  background-color: #ff9f43;
  color: white;
  padding: 5px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  height: 38px;
  min-width: 90px;
}

.pagination {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}

.pagination button {
  padding: 5px;
  margin: 0 4px;
  border: 1px solid #ccc;
  background-color: white;
  border-radius: 4px;
  cursor: pointer;
  min-width: 42px;
  height: 42px;
  border-radius: 50px;
}

.pagination button.active {
  background-color: #4a5d3a;
  color: white;
  border-color: #4a5d3a;
}


.input-group .btn{
  z-index: 0;
}

.btn-outline-secondary{
  background-color: #333;
  color: #fff;
}

.btn-outline-secondary:hover{
    background-color: #282828 !important;
}




