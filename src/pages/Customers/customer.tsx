import "./customer.css";
import type { Customer } from "../../utils/interface";
import { registerAllModules } from 'handsontable/registry';
import React, { useEffect, useRef, useState } from "react";
import { getRequest, postRequest } from "../../utils/requests";
import { HotTable, type HotTableRef } from '@handsontable/react-wrapper';

registerAllModules();

const CustManagement = () => {
  const hotTableRef = useRef<HotTableRef>(null);
  const columns = ['cust_code', 'cust'];

  const [searchTerm, setSearchTerm] = useState<string>("");
  const inputValueRef = useRef<string>(""); // Store input value without triggering API
  const [customerData, setCustomerData] = useState<Customer[]>([]);
  const custHeaders = ['cust_code', 'cust', 'actions'];
  const [custFormVisible, setCustFormVisible] = useState(false);

  const [custNo, setCustNo] = useState("");
  const [cust, setCust] = useState("");

  const [custButtonLabel, setCustButtonLabel] = useState("Add New");

  // Manage all erroes related to rack form
  const [custErrors, setCustErrors] = useState<{
    cust_code?: string;
    cust?: string;
  }>({});

  useEffect(() => {
    const hotInstance = hotTableRef.current;
    console.log('hotInstance :', hotInstance);
    if (!hotInstance) return;
  }, []);

  // Handle OK button click for search
  useEffect(() => {
    const interval = setInterval(() => {
      const okBtn = document.querySelector('.htUIButtonOK') as HTMLElement;

      if (okBtn) {
        okBtn.onclick = () => {
          const searchText = inputValueRef.current;
          console.log('OK button clicked with search text:', searchText);

          // Trigger API call by setting searchTerm
          setSearchTerm(searchText);
        };
        clearInterval(interval); // Only set once
      }
    }, 500);
    return () => clearInterval(interval);
  }, []); // Remove dependency to prevent re-running


  useEffect (()=>{
    fetchCustomerData('', 'desc');
  }, [searchTerm])

  const fetchCustomerData = async (sort_by: string, sort_order: string) => {
    try {
      const response = await getRequest(
        `customers?page=${1}&limit=${10}&search=${searchTerm}&sort_by=${sort_by}&sort_order=${sort_order}`,
        {}
      );
      if (response.data.status == "success") {
        console.log('response.data.data :', response.data.data);
        let custData = response.data.data.data;
        custData = custData.map((item: any) => {
          return {
            id: item.id,
            cust_code: item.cust_code,
            cust: item.cust,
            actions: 'actions' // Placeholder for action column
          }
        })
        setCustomerData(custData)
      } else {
      }
    } catch (error) {
    }
  };

  /**
   * Toggles the visibility of the new rack form and resets the form fields.
   * This function is called when the user wants to add a new rack.
   * It updates the state for the rack form visibility, clears the rack and device inputs,
   * resets the rack button label to "Add New", verifies the device as false,
   * and clears any existing rack errors.
   */
  const newCustForm = () => {
    setCustFormVisible((pre) => !pre);
    setCustNo("");
    setCust("");

    setCustButtonLabel("Add New");
    setCustErrors({});
  };


  // Function to add or Update rack
  const addOrUpdateRack = (e: React.FormEvent, action: string) => {
    e.preventDefault();
    let errors: { cust_code?: string; cust?: string } = {};

    // Validate inputs
    if (!custNo.trim()) {
      errors.cust_code = "Customer No is required";
    }
    if (!cust.trim()) {
      errors.cust = "Customer name is required";
    }
    setCustErrors(errors);
    const payload = {
      cust_code: custNo,
      cust: cust,
    };
    if(Object.keys(errors).length === 0){
      if (action === "Update") {
        // updateRack(payload);
      } else {
        saveCust(payload);
      }
    }
  };


  const saveCust = async (data: { cust_code: string; cust: string }) => {
    try {
      const saveCustResult = await postRequest("customer", data);

      if (saveCustResult.data.status === "success") {
        console.log("Customer saved successfully:", saveCustResult);
        const newCustomer = {
          id: saveCustResult.data.data.id,
          cust_code: saveCustResult.data.data.cust_code,
          cust: saveCustResult.data.data.cust,
          actions: 'actions' // Placeholder for action column
        };
        setCustomerData((prev) => [newCustomer, ...prev]);

      } else {
        console.error("Failed to save customer:", saveCustResult);

      }
    } catch (error: any) {
      console.log('Message :', error.response.data.detail.message || "An unexpected error occurred");
    }
  };

  // Function to handle edit button click
  const handleEdit = (rowIndex: number) => {
    console.log('Editing customer at row index:', rowIndex);
    const customer = customerData[rowIndex];
    if (customer) {
      console.log('Customer data:', customer);
      setCustNo(customer.cust_code);
      setCust(customer.cust);
      setCustButtonLabel("Update");
      setCustFormVisible(true);
    } else {
      console.error('Customer not found at row index:', rowIndex);
    }
  };

  // Function to handle delete button click
  const handleDelete = (rowIndex: number) => {
    console.log('Deleting customer at row index:', rowIndex);
    const customer = customerData[rowIndex];
    if (customer && window.confirm(`Are you sure you want to delete customer ${customer.cust}?`)) {
      // Here you would typically make an API call to delete the customer
      console.log('Deleting customer:', customer);
      setCustomerData((prev) => prev.filter((_, index) => index !== rowIndex));
    }
  };

  const handleColumnSort = (
    _currentSortConfig: any[],
    destinationSortConfigs: any[]
  ) => {
    console.log('Sort config:', { _currentSortConfig, destinationSortConfigs });

    if (destinationSortConfigs.length > 0) {
      const sortColumnIndex = destinationSortConfigs[0].column;
      const sortOrder = destinationSortConfigs[0].sortOrder as 'asc' | 'desc';
      const columnName = columns[sortColumnIndex];

      console.log(`Sorting by column: ${columnName}, order: ${sortOrder}`);
      fetchCustomerData(columnName, sortOrder);
    } else {
      // Handle "default" state - fetch data without sorting (original order)
      console.log('Clearing sort - fetching original order');
      fetchCustomerData('', 'asc'); // Empty sort_by means original order
    }
  };


  return (
    <div className="rack-management-page">
      <div className="header-card">
        <div>
          <h1>Customers</h1>
          <p>Manage and monitor Customers.</p>
        </div>
        <div className="search-rack">
          <input
            type="text"
            // onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search Customer"
          />
        </div>
      </div>
      {!custFormVisible ? (
        <div
          className="d-flex justify-content-end"
          style={{ marginBottom: "20px" }}
        >
          <button className="deploy-btn" onClick={() => newCustForm()}>
            + Add New Customer
          </button>
        </div>
      ) : (
        <div className="deploy-rack-card">
          {/* <h3>Deploy New Rack</h3> */}
          <form onSubmit={(event) => addOrUpdateRack(event, custButtonLabel)}>
            <div className="deploy-rack-form row row-gap-3">
              <div className="col-md-4">
                <div className="d-flex justify-content-between">
                  <label>Cust No</label>
                  {custErrors.cust_code && (
                    <small className="text-danger text-right">
                      {custErrors.cust_code}
                    </small>
                  )}
                </div>
                <input
                  type="text"
                  className="w-100"
                  placeholder="Enter Customet No."
                  value={custNo}
                  onChange={(e) => setCustNo(e.target.value)}
                />
              </div>
              <div className="col-md-5 ">
                <div className="d-flex gap-2 align-items-end">
                  <div className="w-100">
                    <div className="d-flex justify-content-between">
                      <label>Customer</label>
                      {custErrors.cust && (
                        <small className="text-danger text-right">
                          {custErrors.cust}
                        </small>
                      )}
                    </div>
                    <div className="position-relative d-flex">
                      <input
                        type="text"
                        className="w-100"
                        placeholder="Enter Customer Name"
                        value={cust}
                        onChange={(e) => setCust(e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div className="col-md-3">
                <div className="d-flex gap-2">
                  <button className="deploy-btn w-100" type="submit">
                    {custButtonLabel == 'Update' ? 'Update' : 'Save'}
                  </button>
                  <a
                    className="text-decoration-none text-muted cancel-btn"
                    onClick={() => newCustForm()}
                  >
                    Cancel
                  </a>
                </div>
              </div>
            </div>
          </form>


        </div>
      )}
      <HotTable
        ref={hotTableRef}
        themeName="ht-theme-main-dark-auto"
        // other options
        data={customerData && customerData.length > 0 ? customerData : [Array(custHeaders.length).fill('')]}
        colHeaders={custHeaders}
        rowHeaders={true}
        stretchH="all"
        autoColumnSize={true}
        height="auto"
        autoWrapRow={true}
        autoWrapCol={true}
        filters={true}            // Enable Filters plugin
        columnSorting={{ indicator: true }}      // Enable sorting by clicking headers
        readOnly={true}           // Make all cells non-editable
        beforeColumnSort={(currentSortConfig: any, destinationSortConfigs: any) => {
          // Handle the sorting logic
          handleColumnSort(currentSortConfig, destinationSortConfigs);
          // Return false to prevent client-side sorting
          return false;
        }}
        columns={[
          { data: 'cust_code', readOnly: true },
          { data: 'cust', readOnly: true },
          {
            data: 'actions',
            readOnly: true,
            renderer: (_instance: any, td: HTMLElement, row: number) => {
              td.innerHTML = '';
              td.style.textAlign = 'center';
              td.style.verticalAlign = 'middle';

              // Get the customer data for this row
              const customer = customerData[row];
              if (!customer || !customer.id) {
                return td;
              }

              const buttonContainer = document.createElement('div');
              buttonContainer.style.display = 'flex';
              buttonContainer.style.gap = '5px';
              buttonContainer.style.justifyContent = 'center';

              // Edit button
              const editBtn = document.createElement('button');
              editBtn.innerHTML = 'Edit';
              editBtn.className = 'edit-btn';
              editBtn.style.padding = '4px 8px';
              editBtn.style.fontSize = '12px';
              editBtn.style.cursor = 'pointer';
              editBtn.style.backgroundColor = '#007bff';
              editBtn.style.color = 'white';
              editBtn.style.border = 'none';
              editBtn.style.borderRadius = '3px';
              editBtn.onclick = (e) => {
                e.stopPropagation();
                handleEdit(row); // Use row index to get the visible row data
              };

              // Delete button
              const deleteBtn = document.createElement('button');
              deleteBtn.innerHTML = 'Delete';
              deleteBtn.className = 'delete-btn';
              deleteBtn.style.padding = '4px 8px';
              deleteBtn.style.fontSize = '12px';
              deleteBtn.style.cursor = 'pointer';
              deleteBtn.style.backgroundColor = '#dc3545';
              deleteBtn.style.color = 'white';
              deleteBtn.style.border = 'none';
              deleteBtn.style.borderRadius = '3px';
              deleteBtn.onclick = (e) => {
                e.stopPropagation();
                handleDelete(row); // Use row index to get the visible row data
              };

              buttonContainer.appendChild(editBtn);
              buttonContainer.appendChild(deleteBtn);
              td.appendChild(buttonContainer);

              return td;
            }
          }
        ]}
        dropdownMenu={{
          items: {
            searchOnly: {
              name: 'Search',
              key: 'search',
              disableSelection: true,
              renderer(_hot, wrapper) {
                wrapper.innerHTML = '';
                const input = document.createElement('input');
                input.type = 'text';
                input.placeholder = 'Type to search...';
                input.style.width = '150px';
                input.style.margin = '5px';
                input.style.padding = '5px';
                input.style.border = '1px solid #ccc';
                input.style.borderRadius = '4px';

                // Prevent dropdown from closing when clicking inside input
                input.addEventListener('mousedown', e => e.stopPropagation());

                // Store the input value in ref without triggering re-renders
                input.addEventListener('input', (e) => {
                  const target = e.target as HTMLInputElement;
                  inputValueRef.current = target.value;
                });

                wrapper.appendChild(input);
                return wrapper;
              }
            },
            'filter_action_bar': {} //  OK & Cancel buttons
          }
        }}
        licenseKey="non-commercial-and-evaluation"
      />
    </div>
  );
};

export default CustManagement;