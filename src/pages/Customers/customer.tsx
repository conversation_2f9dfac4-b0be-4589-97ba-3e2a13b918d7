import "./customer.css";
import React, { useEffect, useRef, useState } from "react";
import { getRequest } from "../../utils/requests";
import { HotTable, type HotTableRef } from '@handsontable/react-wrapper';
// import Handsontable from 'handsontable';
import { registerAllModules } from 'handsontable/registry';

registerAllModules();

// type HotTableWithInstance = HotTable & { hotInstance: Handsontable };


const CustManagement = () => {
  const hotTableRef = useRef<HotTableRef>(null);

  // const hotTableRef = useRef<HotTableWithInstance | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [inputValue, setInputValue] = useState<string>(""); // Store input value without triggering API
  const [customerData, setCustomerData] = useState<[{
    cust_no: string;
    cust?: string;
  }]>();
  const custHeaders = ['cust_no', 'cust'];
  const [custFormVisible, setCustFormVisible] = useState(false);

  const [custNo, setCustNo] = useState("");
  const [cust, setCust] = useState("");

  const [rackButtonLabel, setRackButtonLabel] = useState("Add New");

  // Manage all erroes related to rack form
  const [rackErrors, setRackErrors] = useState<{
    cust_no?: string;
    cust?: string;
  }>({});

  useEffect(() => {
    // const hotInstance = hotTableRef.current?.hotInstance;
    const hotInstance = hotTableRef.current;
    console.log('hotInstance :', hotInstance);
    if (!hotInstance) return;
  }, []);



  // Handle OK button click for search
  useEffect(() => {
    const interval = setInterval(() => {
      const okBtn = document.querySelector('.htUIButtonOK') as HTMLElement;

      if (okBtn) {
        okBtn.onclick = () => {
          console.log('OK button clicked with search text:', inputValue);
          // Here you would normally call setSearchTerm(inputValue) to trigger API call
          // For now, just console logging as requested
        };
        clearInterval(interval); // Only set once
      }
    }, 500);
    return () => clearInterval(interval);
  }, [inputValue]);



  useEffect (()=>{
    const fetchCustomerData = async () => {
      try {
        const response = await getRequest(
          `customers?page=${1}&limit=${10}&search=${searchTerm}`,
          {}
        );
        if (response.data.status == "success") {
          console.log('response.data.data :', response.data.data);
          let custData = response.data.data.data;
          custData = custData.map((item: any) => {
            return {
              cust_no: item.cust_code,
              cust: item.cust
            }
          })
          setCustomerData(custData)
        } else {
        }
      } catch (error) {
      }
    };
    fetchCustomerData();
  }, [])

  // Commented out automatic API call on searchTerm change
  // useEffect(() => {
  //   const fetchSearchResults = async () => {
  //     if (searchTerm.trim() === "") {
  //       return; // Skip fetching if search term is empty
  //     }
  //     try {
  //       const response = await getRequest(
  //         `customers?page=${1}&limit=${10}&search=${searchTerm}`,
  //         {}
  //       );
  //       if (response.data.status == "success") {
  //         console.log('response.data.data :', response.data.data);
  //         let custData = response.data.data.data;
  //         custData = custData.map((item: any) => {
  //           return {
  //             cust_no: item.cust_code,
  //             cust: item.cust
  //           }
  //         })
  //         setCustomerData(custData)
  //       } else {
  //       }
  //     } catch (error) {
  //     }
  //   };
  //   fetchSearchResults();
  // }, [searchTerm]);


  /**
   * Toggles the visibility of the new rack form and resets the form fields.
   * This function is called when the user wants to add a new rack.
   * It updates the state for the rack form visibility, clears the rack and device inputs,
   * resets the rack button label to "Add New", verifies the device as false,
   * and clears any existing rack errors.
   */
  const newRackForm = () => {
    setCustFormVisible((pre) => !pre);
    setCustNo("");
    setCust("");

    setRackButtonLabel("Add New");
    setRackErrors({});
  };


  // Function to add or Update rack
  const addOrUpdateRack = (e: React.FormEvent, action: string) => {
    return
    // let newErrors: { rack?: string; device?: string } = {};
    // e.preventDefault();
    // if (!rack) {
    //   newErrors.rack = "Rack ID is required";
    // }
    // if (!deviceVerified) {
    //   newErrors.device = "Please Verify the Device";
    // }
    // if (!device) {
    //   newErrors.device = "Please Assign Any device";
    // }
    // setRackErrors(newErrors);
    // if (Object.keys(newErrors).length === 0) {
    //   const newRack = {
    //     rack_name: rack,
    //     device_id: device,
    //   };
    //   if (action === "Update") {
    //     // Logic to update the inventory
    //     const updateRack = async () => {
    //       try {
    //         const response = await putRequest(`rack/${rackEditing}`, newRack);
    //         if (response) {
    //           setRackEditing(response.data.rack.rack_name);
    //           toast.dismiss();
    //           toast.success("Rack updated successfully!");
    //           setRacksData((prev) =>
    //             prev.map((item) =>
    //               item.id === response.data.rack.id ? response.data.rack : item
    //             )
    //           );
    //         }
    //       } catch (error: any) {
    //         toast.dismiss();
    //         toast.error(
    //           error.response.data.detail ||
    //             "Failed to update rack. Please try again."
    //         );
    //       }
    //     };
    //     updateRack();
    //   } else {
    //     // Logic to add new inventory
    //     const addNewRack = async () => {
    //       try {
    //         const response = await postRequest("rack", newRack);
    //         if (response) {
    //           toast.dismiss();
    //           toast.success("Rack added successfully!");
    //           setRack("");
    //           setDevice("");
    //           setRacksData((prev) => [response.data.rack, ...prev]);
    //         }
    //       } catch (error: any) {
    //         toast.dismiss();
    //         toast.error(
    //           error.response.data.detail ||
    //             "Failed to add rack. Please try again."
    //         );
    //       }
    //     };
    //     addNewRack();
    //   }
    // }
  };

  return (
    <div className="rack-management-page">
      <div className="header-card">
        <div>
          <h1>Customers</h1>
          <p>Manage and monitor Customers.</p>
        </div>
        <div className="search-rack">
          <input
            type="text"
            // onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search Customer"
          />
        </div>
      </div>
      {!custFormVisible ? (
        <div
          className="d-flex justify-content-end"
          style={{ marginBottom: "20px" }}
        >
          <button className="deploy-btn" onClick={() => newRackForm()}>
            + Add New Customer
          </button>
        </div>
      ) : (
        <div className="deploy-rack-card">
          {/* <h3>Deploy New Rack</h3> */}
          <form onSubmit={(event) => addOrUpdateRack(event, rackButtonLabel)}>
            <div className="deploy-rack-form row row-gap-3">
              <div className="col-md-4">
                <div className="d-flex justify-content-between">
                  <label>Cust No</label>
                  {rackErrors.cust_no && (
                    <small className="text-danger text-right">
                      {rackErrors.cust_no}
                    </small>
                  )}
                </div>
                <input
                  type="text"
                  className="w-100"
                  placeholder="Enter Customet No."
                  value={custNo}
                  onChange={(e) => setCustNo(e.target.value)}
                />
              </div>
              <div className="col-md-5 ">
                <div className="d-flex gap-2 align-items-end">
                  <div className="w-100">
                    <div className="d-flex justify-content-between">
                      <label>Customer</label>
                      {rackErrors.cust && (
                        <small className="text-danger text-right">
                          {rackErrors.cust}
                        </small>
                      )}
                    </div>
                    <div className="position-relative d-flex">
                      <input
                        type="text"
                        className="w-100"
                        placeholder="Enter Customer Name"
                        value={cust}
                        onChange={(e) => setCust(e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div className="col-md-3">
                <div className="d-flex gap-2">
                  <button className="deploy-btn w-100" type="submit">
                    {rackButtonLabel == 'Update' ? 'Update' : 'Save'}
                  </button>
                  <a
                    className="text-decoration-none text-muted cancel-btn"
                    onClick={() => newRackForm()}
                  >
                    Cancel
                  </a>
                </div>
              </div>
            </div>
          </form>


        </div>
      )}
      <HotTable
        themeName="ht-theme-main-dark-auto"
        // other options
        data={customerData && customerData.length > 0 ? customerData : [Array(custHeaders.length).fill('')]}
        colHeaders={custHeaders}
        rowHeaders={true}
        stretchH="all"
        autoColumnSize={true}
        height="auto"
        autoWrapRow={true}
        autoWrapCol={true}
        filters={true}            // Enable Filters plugin
        columnSorting={true}      // Enable sorting by clicking headers
        readOnly={true}           // Make all cells non-editable
        dropdownMenu={{
          items: {
            searchOnly: {
              name: 'Search',
              key: 'search',
              disableSelection: true,
              renderer(_hot, wrapper) {
                wrapper.innerHTML = '';
                const input = document.createElement('input');
                input.type = 'text';
                input.placeholder = 'Type to search...';
                input.style.width = '150px';
                input.style.margin = '5px';
                input.style.padding = '5px';
                input.style.border = '1px solid #ccc';
                input.style.borderRadius = '4px';

                // Prevent dropdown from closing when clicking inside input
                input.addEventListener('mousedown', e => e.stopPropagation());

                // Store the input value in state without triggering API calls
                input.addEventListener('input', (e) => {
                  const target = e.target as HTMLInputElement;
                  setInputValue(target.value);
                });

                wrapper.appendChild(input);
                return wrapper;
              }
            },
            'filter_action_bar': {} //  OK & Cancel buttons
          }
        }}
        licenseKey="non-commercial-and-evaluation"
        // dropdownMenu={{
        //   items: {
        //     searchOnly: {
        //       name: 'Search',
        //       key: 'search',
        //       disableSelection: true,
        //       renderer(_hot, wrapper) {
        //         wrapper.innerHTML = ''; // clear default content
        //         const input = document.createElement('input');
        //         input.type = 'text';
        //         input.placeholder = 'Type to search...';
        //         input.style.width = '150px';
        //         input.style.padding = '5px';
        //         input.style.margin = '5px';
        //         input.style.border = '1px solid #ccc';
        //         input.style.borderRadius = '4px';

        //         // ✅ Prevent dropdown from closing when clicking inside input
        //         input.addEventListener('mousedown', (e) => {
        //           e.stopPropagation();
        //         });

        //         // // ✅ Create container for buttons
        //         // const buttonContainer = document.createElement('div');
        //         // buttonContainer.style.display = 'flex';
        //         // buttonContainer.style.justifyContent = 'space-between';
        //         // buttonContainer.style.margin = '5px';

        //         // // ✅ OK button
        //         // const okBtn = document.createElement('button');
        //         // okBtn.innerText = 'OK';
        //         // okBtn.style.padding = '4px 10px';
        //         // okBtn.style.cursor = 'pointer';
        //         // okBtn.style.background = '#4CAF50';
        //         // okBtn.style.color = '#fff';
        //         // okBtn.style.border = 'none';
        //         // okBtn.style.borderRadius = '4px';
        //         // okBtn.addEventListener('mousedown', (e) => e.stopPropagation());
        //         // okBtn.addEventListener('click', () => {
        //         //   console.log('OK clicked with value:', input.value);
        //         //   setSearchTerm(input.value); // ✅ Apply search term
        //         // });

        //         // // ✅ Cancel button
        //         // const cancelBtn = document.createElement('button');
        //         // cancelBtn.innerText = 'Cancel';
        //         // cancelBtn.style.padding = '4px 10px';
        //         // cancelBtn.style.cursor = 'pointer';
        //         // cancelBtn.style.background = '#f44336';
        //         // cancelBtn.style.color = '#fff';
        //         // cancelBtn.style.border = 'none';
        //         // cancelBtn.style.borderRadius = '4px';
        //         // cancelBtn.addEventListener('mousedown', (e) => e.stopPropagation());
        //         // cancelBtn.addEventListener('click', () => {
        //         //   console.log('Cancel clicked');
        //         //   _hot.getPlugin('dropdownMenu').close();
        //         //   setSearchTerm(''); // ✅ Clear search term
        //         //   input.addEventListener('keydown', (e) => {
        //         //     // if (e.key === 'Escape') {
        //         //       console.log('ESC pressed');
        //         //       // input.value = '';
        //         //     // }
        //         //   });
        //         // });

        //         // buttonContainer.appendChild(okBtn);
        //         // buttonContainer.appendChild(cancelBtn);

        //         // ✅ Append input and buttons
        //         wrapper.appendChild(input);

        //         return wrapper;
        //       }
        //     }
        //   }
        // }}
        // afterDropdownMenuShow={() => {
        //   setTimeout(() => {
        //     // Hide checkbox list
        //     const checkboxList = document.querySelectorAll('.htUIMultipleSelectHot');
        //     checkboxList.forEach(el => (el as HTMLElement).style.display = 'none');

        //     // Hide "Select all" and "Clear"
        //     const selectClearLinks = document.querySelectorAll('.htUISelectAll, .htUIClearAll');
        //     selectClearLinks.forEach(el => (el as HTMLElement).style.display = 'none');

        //     // Remove extra space (container adjustment)
        //     const filterValueSection = document.querySelector('.htFiltersMenuValue');
        //     if (filterValueSection) {
        //       (filterValueSection as HTMLElement).style.paddingBottom = '0';
        //       (filterValueSection as HTMLElement).style.height = 'auto';
        //     }

        //     // Attach input listener
        //     const input = document.querySelector<HTMLInputElement>(
        //       '.htFiltersMenuCondition input'
        //     );

        //     if (input) {
        //       input.addEventListener('input', (e: Event) => {
        //         const target = e.target as HTMLInputElement;
        //         customFunction(target.value);
        //       });
        //     }
        //   }, 150);
        // }}
      />
    </div>
  );
};

export default CustManagement;