import "./customer.css";
import React, { useEffect, useRef, useState } from "react";
import { getRequest, postRequest } from "../../utils/requests";
import { HotTable, type HotTableRef } from '@handsontable/react-wrapper';
import { registerAllModules } from 'handsontable/registry';
import type { Customer } from "../../utils/interface";

registerAllModules();

const CustManagement = () => {
  const hotTableRef = useRef<HotTableRef>(null);

  const [searchTerm, setSearchTerm] = useState<string>("");
  const inputValueRef = useRef<string>(""); // Store input value without triggering API
  const [customerData, setCustomerData] = useState<Customer[]>([]);
  const custHeaders = ['cust_no', 'cust'];
  const [custFormVisible, setCustFormVisible] = useState(false);

  const [custNo, setCustNo] = useState("");
  const [cust, setCust] = useState("");

  const [custButtonLabel, setCustButtonLabel] = useState("Add New");

  // Manage all erroes related to rack form
  const [custErrors, setCustErrors] = useState<{
    cust_no?: string;
    cust?: string;
  }>({});

  useEffect(() => {
    const hotInstance = hotTableRef.current;
    console.log('hotInstance :', hotInstance);
    if (!hotInstance) return;
  }, []);

  // Handle OK button click for search
  useEffect(() => {
    const interval = setInterval(() => {
      const okBtn = document.querySelector('.htUIButtonOK') as HTMLElement;

      if (okBtn) {
        okBtn.onclick = () => {
          const searchText = inputValueRef.current;
          console.log('OK button clicked with search text:', searchText);
          
          // Trigger API call by setting searchTerm
          setSearchTerm(searchText);
        };
        clearInterval(interval); // Only set once
      }
    }, 500);
    return () => clearInterval(interval);
  }, []); // Remove dependency to prevent re-running


  useEffect (()=>{
    const fetchCustomerData = async () => {
      try {
        const response = await getRequest(
          `customers?page=${1}&limit=${10}&search=${searchTerm}`,
          {}
        );
        if (response.data.status == "success") {
          console.log('response.data.data :', response.data.data);
          let custData = response.data.data.data;
          custData = custData.map((item: any) => {
            return {
              cust_no: item.cust_code,
              cust: item.cust
            }
          })
          setCustomerData(custData)
        } else {
        }
      } catch (error) {
      }
    };
    fetchCustomerData();
  }, [])

  // API call triggered only when searchTerm changes (from OK button click)
  useEffect(() => {
    const fetchSearchResults = async () => {
      if (searchTerm.trim() === "") {
        return; // Skip fetching if search term is empty
      }
      try {
        console.log('Fetching data for search term:', searchTerm);
        const response = await getRequest(
          `customers?page=${1}&limit=${10}&search=${searchTerm}`,
          {}
        );
        if (response.data.status == "success") {
          console.log('API response data:', response.data.data);
          let custData = response.data.data.data;
          custData = custData.map((item: any) => {
            return {
              cust_no: item.cust_code,
              cust: item.cust
            }
          })
          setCustomerData(custData)
        } else {
          console.log('API call failed with status:', response.data.status);
        }
      } catch (error) {
        console.error('Error fetching search results:', error);
      }
    };
    fetchSearchResults();
  }, [searchTerm]);


  /**
   * Toggles the visibility of the new rack form and resets the form fields.
   * This function is called when the user wants to add a new rack.
   * It updates the state for the rack form visibility, clears the rack and device inputs,
   * resets the rack button label to "Add New", verifies the device as false,
   * and clears any existing rack errors.
   */
  const newCustForm = () => {
    setCustFormVisible((pre) => !pre);
    setCustNo("");
    setCust("");

    setCustButtonLabel("Add New");
    setCustErrors({});
  };


  // Function to add or Update rack
  const addOrUpdateRack = (e: React.FormEvent, action: string) => {
    e.preventDefault();
    let errors: { cust_no?: string; cust?: string } = {};
    
    // Validate inputs
    if (!custNo.trim()) {
      errors.cust_no = "Customer No is required";
    }
    if (!cust.trim()) {
      errors.cust = "Customer name is required";
    }
    setCustErrors(errors);
    const payload = {
      cust_code: custNo,
      cust: cust,
    };
    if(Object.keys(errors).length === 0){
      if (action === "Update") {
        // updateRack(payload);
      } else {
        saveCust(payload);
      }
    }
  };

  
  const saveCust = async (data: { cust_code: string; cust: string }) => {
    try {
      const saveCustResult = await postRequest("customer", data);
  
      if (saveCustResult.data.status === "success") {
        console.log("Customer saved successfully:", saveCustResult);
        const newCustomer = {
          cust_no: saveCustResult.data.data.cust_code,
          cust: saveCustResult.data.data.cust,
        };
        setCustomerData((prev) => [newCustomer, ...prev]);

      } else {
        console.error("Failed to save customer:", saveCustResult);

      }
    } catch (error: any) {
      console.log('Message :', error.response.data.detail.message || "An unexpected error occurred");
    }
  };


  return (
    <div className="rack-management-page">
      <div className="header-card">
        <div>
          <h1>Customers</h1>
          <p>Manage and monitor Customers.</p>
        </div>
        <div className="search-rack">
          <input
            type="text"
            // onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search Customer"
          />
        </div>
      </div>
      {!custFormVisible ? (
        <div
          className="d-flex justify-content-end"
          style={{ marginBottom: "20px" }}
        >
          <button className="deploy-btn" onClick={() => newCustForm()}>
            + Add New Customer
          </button>
        </div>
      ) : (
        <div className="deploy-rack-card">
          {/* <h3>Deploy New Rack</h3> */}
          <form onSubmit={(event) => addOrUpdateRack(event, custButtonLabel)}>
            <div className="deploy-rack-form row row-gap-3">
              <div className="col-md-4">
                <div className="d-flex justify-content-between">
                  <label>Cust No</label>
                  {custErrors.cust_no && (
                    <small className="text-danger text-right">
                      {custErrors.cust_no}
                    </small>
                  )}
                </div>
                <input
                  type="text"
                  className="w-100"
                  placeholder="Enter Customet No."
                  value={custNo}
                  onChange={(e) => setCustNo(e.target.value)}
                />
              </div>
              <div className="col-md-5 ">
                <div className="d-flex gap-2 align-items-end">
                  <div className="w-100">
                    <div className="d-flex justify-content-between">
                      <label>Customer</label>
                      {custErrors.cust && (
                        <small className="text-danger text-right">
                          {custErrors.cust}
                        </small>
                      )}
                    </div>
                    <div className="position-relative d-flex">
                      <input
                        type="text"
                        className="w-100"
                        placeholder="Enter Customer Name"
                        value={cust}
                        onChange={(e) => setCust(e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div className="col-md-3">
                <div className="d-flex gap-2">
                  <button className="deploy-btn w-100" type="submit">
                    {custButtonLabel == 'Update' ? 'Update' : 'Save'}
                  </button>
                  <a
                    className="text-decoration-none text-muted cancel-btn"
                    onClick={() => newCustForm()}
                  >
                    Cancel
                  </a>
                </div>
              </div>
            </div>
          </form>


        </div>
      )}
      <HotTable
        themeName="ht-theme-main-dark-auto"
        // other options
        data={customerData && customerData.length > 0 ? customerData : [Array(custHeaders.length).fill('')]}
        colHeaders={custHeaders}
        rowHeaders={true}
        stretchH="all"
        autoColumnSize={true}
        height="auto"
        autoWrapRow={true}
        autoWrapCol={true}
        filters={true}            // Enable Filters plugin
        columnSorting={true}      // Enable sorting by clicking headers
        readOnly={true}           // Make all cells non-editable
        dropdownMenu={{
          items: {
            searchOnly: {
              name: 'Search',
              key: 'search',
              disableSelection: true,
              renderer(_hot, wrapper) {
                wrapper.innerHTML = '';
                const input = document.createElement('input');
                input.type = 'text';
                input.placeholder = 'Type to search...';
                input.style.width = '150px';
                input.style.margin = '5px';
                input.style.padding = '5px';
                input.style.border = '1px solid #ccc';
                input.style.borderRadius = '4px';

                // Prevent dropdown from closing when clicking inside input
                input.addEventListener('mousedown', e => e.stopPropagation());

                // Store the input value in ref without triggering re-renders
                input.addEventListener('input', (e) => {
                  const target = e.target as HTMLInputElement;
                  inputValueRef.current = target.value;
                });

                wrapper.appendChild(input);
                return wrapper;
              }
            },
            'filter_action_bar': {} //  OK & Cancel buttons
          }
        }}
        licenseKey="non-commercial-and-evaluation"
      />
    </div>
  );
};

export default CustManagement;