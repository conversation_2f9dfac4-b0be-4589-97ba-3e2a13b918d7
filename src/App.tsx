import './App.css'
import Login from './auth/login/login';
import { useEffect, useState } from 'react'
import 'bootstrap/dist/css/bootstrap.min.css';
import LayoutManager from './layout/layoutManager';
import {
  HashRouter as Router,
  Route,
  Routes
} from "react-router-dom";
import CustManagement from './pages/Customers/customer';
import 'handsontable/styles/handsontable.min.css';
import 'handsontable/styles/ht-theme-main.min.css';


function App() {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  useEffect(() => {
    const username = localStorage.getItem("username");
    setIsAuthenticated(!!username);
  }, [location]);
  if (isAuthenticated === null) return null; // Or show a loader
  return (
    <Router>
      <Routes>
        {/* Login route */}
        <Route path="/login" element={<Login />} />
        {/* Routes with Layout */}
        <Route
          path="/*"
          element={
            <LayoutManager>
              <Routes>
                <Route path="/cust" element={<CustManagement />} />
                {/* Add more routes here */}
              </Routes>
            </LayoutManager>
          }
        />
        <Route path="*" element={<Login />} />
      </Routes>
    </Router>
  )
}

export default App
