.app-sidebar {
    /* height: 100vh; */
    border-top: none;
    transition: all 0.3s ease;
    background-color: #f7f7f7 !important;
    overflow: auto;
}

.app-sidebar ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.app-sidebar li {
    margin: 10px 0;
}

.app-sidebar a {
    color: #333;
    text-decoration: none;
    padding: 10px 20px;
    display: block;
    background: #ebebeb;
    border-radius: 4px;
}

.app-sidebar a.active {
    /* background-color: #2c5f34; */
    background-color: #4a5d3a;
    color: white;
}

.sidebar-closed {
    width: 0;
    padding: 0 !important;
    opacity: 0;
}
    
.sidebar-open {
    min-width: 220px;
    opacity: 1;
    padding: 1rem 8px;
}

.app-sidebar a:hover {
    background-color: #2c5f343e;
    border-radius: 4px;

}

.app-sidebar a.active {
    border-radius: 4px;
    /* background-color: #2c5f34; */
    background-color: #4a5d3a;
}