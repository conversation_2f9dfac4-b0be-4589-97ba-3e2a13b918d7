import React, { useEffect, useState } from "react";
import { NavLink } from "react-router-dom";
import "./sidebar.css";

type SidebarProps = {
  isOpen: boolean;
};
const Sidebar: React.FC<SidebarProps> = ({ isOpen }) => {
  const [userRole, setUserRole] = useState<string>("admin");

  useEffect(() => {
    const role = localStorage.getItem("role"); // Get role from localStorage
    setUserRole(role || "admin");
  }, []);

  return (
    // <aside className={`sidebar bg-light p-3 app-sidebar ${isOpen ? 'open' : 'closed'}`}>
    <aside
      className={`bg-light p-3 app-sidebar ${
        isOpen ? "sidebar-open" : "sidebar-closed"
      }`}
    >
      <nav>
        <ul>
          <li>
            <NavLink to="/cust">Customer</NavLink>
          </li>
          {userRole === "super admin" && (
            <li>
              <NavLink to="/users">Users</NavLink>
            </li>
          )}
          <li>
            <NavLink to="/rack">Rack</NavLink>
          </li>
          <li>
            <NavLink to="/inventory">Inventory</NavLink>
          </li>
        </ul>
      </nav>
    </aside>
  );
};

export default Sidebar;
