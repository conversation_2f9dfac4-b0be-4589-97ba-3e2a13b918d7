/* Layout */

/* .app-layout {
    height: 100vh;
    min-height: calc(100vh - 56px);
} */

.app-layout {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    padding-bottom: 45px;
}
.app-body {
    display: flex;
    flex: 1;
    overflow: hidden;
}
.app-content {
    flex: 1;
    padding: 20px;
    width: 100%;
}

.main-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
}

.content-area {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background-color: #f9f9f9;
}


