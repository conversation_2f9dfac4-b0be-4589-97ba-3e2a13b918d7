import React, { useState } from "react";
import "./layoutManager.css";
import Header from "./header/header";
import Sidebar from "./sidebar/sidebar";
type LayoutProps = {
  children: React.ReactNode;
};

const LayoutManager: React.FC<LayoutProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  /**
   * Side bar toggle function using hamburger icon through header
   */
  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };
  return (
    <>
      <div className="app-layout">
        <Header onToggleSidebar={toggleSidebar} />
        <div className="app-body">
          <Sidebar isOpen={sidebarOpen} />
          <main className="app-content">{children}</main>
        </div>
      </div>
    </>
  );
};

export default LayoutManager;
