import "./header.css";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { getRequest } from "../../utils/requests";

type HeaderProps = {
  onToggleSidebar: () => void;
};

const Header: React.FC<HeaderProps> = ({ onToggleSidebar }) => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [deviceConnected, setDeviceConnected] = useState<boolean>(true);
  const deviceIdToVerify = "00000000";
  const isAuthenticated = localStorage.getItem("username");
  const onLogout = () => {
    localStorage.removeItem("username");
    localStorage.removeItem("token");
    navigate("/login");
  };

  useEffect(() => {
    const fetchSearchResults = async () => {
      if (searchTerm.trim() === "") {
        return; // Skip fetching if search term is empty
      }
      try {
        const response = await getRequest(
          `inventories?query=${searchTerm}&skip=${0}&limit=${20}`,
          {}
        );
        if (response.statusText === "OK") {
          setSearchResults(response.data.items);
        } else {
          setSearchResults([]);
        }
      } catch (error) {
        setSearchResults([]);
      }
    };

    fetchSearchResults();
  }, [searchTerm]);


  useEffect(() => {
  }, []);

  const handleItemClick = (inventoryType: string) => {
    setSearchTerm("");
    navigate(`/inventory?type=${encodeURIComponent(inventoryType)}`);
  };

  return (
    <header className="app-header">
      <div className="gap-4 d-flex ">
        {!deviceConnected && (
          <div className="no-input-connect">
            <h1>Please Connect Device!</h1>
          </div>
        )}
        <div>
          <button className="menu-toggle" onClick={onToggleSidebar}>
            ☰
          </button>
          <span className="logo">HEADER!</span>
        </div>
        {/* <div className="search-box my-auto">
          <input
            type="text"
            name="search"
            className=""
            placeholder="Search inventory..."
            style={{ width: "250px" }}
          />
        </div> */}
        <div className="search-box my-auto">
          <input
            type="search"
            name="search"
            id="search"
            placeholder="Search Inventory"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="border-2 rounded-lg outline-0 p-2 h-10 w-64"
          />
          {searchTerm && searchResults.length > 0 && (
            <ul className="list-group position-absolute mt-1 bg-white border w-100 rounded shadow text-dark ">
              {searchResults.map((result) => (
                <li
                  className="list-group-item searched-inventory p-2 m-1 border-0"
                  onClick={() => handleItemClick(result.inventory_type)}
                  style={{ cursor: "pointer" }}
                  key={result.inventory_type}
                >
                  {result.inventory_type}
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>
      <div>
        <div className="user-dropdown dropdown">
          <button
            className="dropdown-toggle border-none p-0"
            type="button"
            id="userDropDown"
            data-bs-toggle="dropdown"
            aria-expanded="false"
          >
            {isAuthenticated}
          </button>
          <ul className="dropdown-menu" aria-labelledby="userDropDown">
            <li className="category-dropdown-item" onClick={onLogout}>
              Logout
            </li>{" "}
          </ul>
        </div>
      </div>
    </header>
  );
};

export default Header;
