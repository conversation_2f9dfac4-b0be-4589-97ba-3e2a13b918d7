/* .app-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    border-bottom: 1px solid #ddd;
} */
.no-input-connect {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
}

.app-header  {
    /* background-color: #2c5f34; */
    background-color: #4a5d3a;
    /* border: 1px solid black; */
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    /* background-color: #f8f9fa; */
    padding: 10px 15px;
}

.menu-toggle {
    font-size: 20px;
    background: none;
    border: none;
    cursor: pointer;
    margin-right: 12px;
    padding: 0;
    color: white; /* Keep the icon in white */
}

.logo {
    font-size: 20px;
    font-weight: bold;
    letter-spacing: 0.5px;
}

.search-box input[type="text"]{
    padding: 8px 40px 8px 12px;
    /* padding: 5px 10px; */
    border-radius: 20px;
    border: none;
    outline: none;
    width: 250px;
    font-size: 14px;
}
.search-box {
    position: relative;
}

@media screen and (max-width: 580px) {
    .search-box {
        display: none;
    }
}

.search-box .search-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #555;
    pointer-events: none;
}

.user-dropdown {
    font-weight: 500;
    cursor: pointer;
}

.user-dropdown .dropdown-toggle{
    border: none;
    background: transparent;
    color: #fff;
    font-weight: normal;
}

.user-dropdown .dropdown-menu li.category-dropdown-item {
    padding: 5px 15px;
    text-align: left;
}
