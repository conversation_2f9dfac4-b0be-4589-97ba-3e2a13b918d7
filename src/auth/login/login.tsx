import React, { useState } from "react";
import "./login.css";
import { Navigate } from "react-router-dom";
import { postRequest } from "../../utils/requests";

const Login: React.FC = () => {
  const isAuthenticated = localStorage.getItem("username");
  // If already logged in, redirect to dashboard
  console.log("isAuthenticated", isAuthenticated)
  if (isAuthenticated) {
    return <Navigate to="/cust" replace />;
  }
  // const navigate = useNavigate();
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [errors, setErrors] = useState<{
    username?: string,
    password?: string
  }>({});

  // const navigate = useNavigate();
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    let newErrors: { username?: string; password?: string } = {};
    if (!username.trim()) {
      newErrors.username = "Username is required!";
    }
    if (!password.trim()) {
      newErrors.password = "Password is required!";
    }
    setErrors(newErrors);
    if (Object.keys(newErrors).length === 0) {
      const data = {
        username: username,
        password: password,
      };
      try {
        const loginResult = await postRequest("login", data);
        if (loginResult.data.status === "success") {
        console.log('loginResult :', loginResult);
          localStorage.setItem("username", username);
          localStorage.setItem("token", loginResult.data.data.access_token);
          
          // const userRole = await getRequest(`user/${username}`, {}, navigate);
          // if (userRole.statusText == "OK") {
          //   localStorage.setItem("role", userRole.data.role);
          // }
          // // Submit form logic
          // toast.dismiss();
          // toast.success("Logged in Successfully!");
          // navigate("/rack");
        }
      } catch (error: any) {
        
      }
    }
  };

  return (
    <div className="login-page-bg container-fluid vh-100 d-flex align-items-center justify-content-center">
      <div className="row w-75 shadow-lg rounded login-container">
        {/* Left Side - Form */}
        <div className="col-md-6 bg-white p-5">
          <div className="text-start mb-4">
            <h3 className="fw-bold mb-1">Inventory Management</h3>
            <p className="text-muted">
              Manage and monitor your server infrastructure
            </p>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="mb-3">
              <div className="d-flex justify-content-between">
                <label className="form-label fw-semibold">
                  Username or Email*
                </label>
                {errors.username && (
                  <small className="text-danger text-right">
                    {errors.username}
                  </small>
                )}
              </div>
              <input
                type="text"
                className="form-control"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                placeholder="Enter username"
              />
            </div>

            <div className="mb-3">
              <div className="d-flex justify-content-between">
                <label className="form-label fw-semibold">Password*</label>{" "}
                {errors.password && (
                  <small className="text-danger">{errors.password}</small>
                )}
              </div>
              <input
                type="password"
                className="form-control"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter password"
              />
            </div>

            {/* <div className="d-flex justify-content-between align-items-center mb-1">
              <a href="#" className="text-decoration-none text-muted">
                Forgot password?
              </a>
            </div> */}

            <button type="submit" className="btn deploy-btn w-100 mb-3">
              Log In
            </button>
          </form>

          {/* <div className="text-center text-muted my-3">
            — or continue with —
          </div> */}

          {/* <div className="d-flex justify-content-between">
            <button className="btn btn-outline-secondary w-100 me-2">
              <img src="https://img.icons8.com/color/16/google-logo.png" alt="Google" className="me-2" />
              Google
            </button>

            <button className="btn btn-outline-secondary w-100 ms-2">
              <img src="https://img.icons8.com/material-outlined/16/github.png" alt="GitHub" className="me-2" />
              GitHub
            </button>
          </div> */}
        </div>

        {/* Right Side - Info */}
        <div className="login-col-right col-md-6 d-flex flex-column justify-content-center align-items-center text-white p-5">
          <div className="text-center">
            {/* <img src="https://img.icons8.com/fluency/96/server.png" alt="Server" className="mb-4" /> */}
            <h1 className="fw-bold mb-3">Server Infrastructure</h1>
            <p className="fs-6">
              Centralized management for all your server racks and devices
            </p>
          </div>
        </div>
      </div>
    </div>
    
  );
};

export default Login;
