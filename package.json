{"name": "joint-control-centre-react", "private": true, "version": "0.0.0", "type": "module", "main": "electron.cjs", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "electron": "electron .", "dist": "electron-builder"}, "dependencies": {"@handsontable/react": "^16.0.1", "@handsontable/react-wrapper": "^16.0.1", "axios": "^1.6.0", "bootstrap": "^5.3.7", "handsontable": "^16.0.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.7.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/axios": "^0.14.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "electron": "^37.2.4", "electron-builder": "^26.0.12", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}, "build": {"appId": "com.army.joint-control", "productName": "JCC", "directories": {"output": "dist"}, "files": ["dist/**/*", "electron.cjs", "python/**", "../joint-control-centre-python/dist/**"], "extraResources": [{"from": "../joint-control-centre-python/dist", "to": "python"}], "win": {"target": "nsis", "icon": "icon.ico"}, "nsis": {"oneClick": false, "perMachine": true, "allowElevation": true, "allowToChangeInstallationDirectory": true}}}