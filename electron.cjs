const { app, BrowserWindow } = require("electron");
const path = require("path");
const { spawn, exec } = require("child_process");
const fs = require("fs");

let mainWindow;
let pythonProcess;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1700,
    height: 800,
    webPreferences: {
      contextIsolation: true,
      nodeIntegration: false, // safer
    },
  });

  // Load your React build index.html
  mainWindow.loadFile(path.join(__dirname, "dist", "index.html"));

  mainWindow.on("closed", function () {
    mainWindow = null;
    if (pythonProcess) {
      pythonProcess.kill();
    }
  });
}

function startPythonBackend() {
  let backendExe;

  if (!app.isPackaged) {
    // LOCAL PATH during `npm run electron`
    backendExe = path.join(
      __dirname,
      "..",
      "joint-control-centre-python",
      "dist",
      "app.exe"
    );
    console.log("🛠 Dev mode: using local backend:", backendExe);
  } else {
    // PACKAGED PATH after `npm run dist`
    backendExe = path.join(
      process.resourcesPath,
      "python",
      "app.exe"
    );
    console.log("📦 Packaged mode: using backend:", backendExe);
  }

  if (!fs.existsSync(backendExe)) {
    console.error("❌ Backend app.exe NOT found at:", backendExe);
    return;
  }

  // Spawn the Python FastAPI backend
  pythonProcess = spawn(backendExe, [], {
    cwd: path.dirname(backendExe),
    env: {
      ...process.env,
      APPDATA: process.env.APPDATA || path.join(require("os").homedir(), "AppData", "Roaming")
    }
  });

  // Optional: log output to console
  pythonProcess.stdout.on("data", (data) => {
    console.log(`🔵 Backend stdout: ${data}`);
  });

  pythonProcess.stderr.on("data", (data) => {
    console.error(`🔴 Backend stderr: ${data}`);
  });

  pythonProcess.on("error", (err) => {
    console.error("❌ Failed to start backend:", err);
  });

  pythonProcess.on("close", (code) => {
    console.log(`⚠️ Backend exited with code ${code}`);
  });
}

function killPort(port, callback) {
  const command = `netstat -ano | findstr :${port}`;
  exec(command, (err, stdout) => {
    if (err || !stdout) {
      console.log(`ℹ️ No process running on port ${port}`);
      return callback();
    }

    const lines = stdout.trim().split("\n");
    const pids = new Set();

    lines.forEach((line) => {
      const parts = line.trim().split(/\s+/);
      const pid = parts[parts.length - 1];
      if (pid) {
        pids.add(pid);
      }
    });

    let pending = pids.size;
    if (pending === 0) return callback();

    pids.forEach((pid) => {
      exec(`taskkill /PID ${pid} /F`, (killErr) => {
        if (killErr) {
          console.error(`⚠️ Failed to kill PID ${pid}:`, killErr);
        } else {
          console.log(`✅ Killed PID ${pid}`);
        }

        if (--pending === 0) {
          callback();
        }
      });
    });
  });
}

app.on("ready", () => {
  killPort(5000, () => {
    startPythonBackend();
    createWindow();
  });
});

app.on("window-all-closed", function () {
  if (pythonProcess) {
    pythonProcess.kill();
  }

  if (process.platform !== "darwin") {
    app.quit();
  }
});

app.on("activate", function () {
  if (mainWindow === null) {
    createWindow();
  }
});
